'use client';

import React from 'react';
import Link from 'next/link';
import { useTranslations, useLocale } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Smartphone, 
  Heart, 
  Users, 
  CheckCircle, 
  Activity, 
  Bell,
  Brain,
  Shield
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ProcessIndicatorProps {
  icon: React.ReactNode;
  value: string;
  label: string;
  status?: 'active' | 'optimized' | 'automated';
}

function ProcessIndicator({ icon, value, label, status = 'active' }: ProcessIndicatorProps) {
  const statusColors = {
    active: 'text-blue-600 bg-blue-50 border-blue-200 dark:text-blue-400 dark:bg-blue-950 dark:border-blue-800',
    optimized: 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-950 dark:border-green-800',
    automated: 'text-purple-600 bg-purple-50 border-purple-200 dark:text-purple-400 dark:bg-purple-950 dark:border-purple-800'
  };

  return (
    <div className="flex items-center space-x-3 text-center sm:text-left">
      <div className={cn(
        "flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center border",
        statusColors[status]
      )}>
        {icon}
      </div>
      <div className="min-w-0 flex-1">
        <div className="text-xl font-bold text-foreground">{value}</div>
        <div className="text-sm text-muted-foreground leading-tight">{label}</div>
      </div>
    </div>
  );
}

export function DigitalizationHeroSection() {
  const t = useTranslations('digitalization');
  const tCta = useTranslations('cta');
  const locale = useLocale();

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-green-50/30 dark:to-green-950/20">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5" />

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-20 lg:py-24">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          {/* Content Column */}
          <div className="space-y-8 order-2 lg:order-1">
            {/* Service Badge */}
            <div className="flex justify-center lg:justify-start">
              <Badge variant="secondary" className="px-4 py-2 text-sm font-medium bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800">
                <Smartphone className="w-4 h-4 mr-2" />
                {t('badge')}
              </Badge>
            </div>

            {/* Main Headline */}
            <div className="space-y-4 text-center lg:text-left">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight text-foreground leading-tight">
                <span className="text-green-600">{t('hero.title.highlight')}</span>
                <br />
                {t('hero.title.main')}
              </h1>

              <p className="text-xl text-muted-foreground font-medium leading-relaxed">
                {t('hero.subtitle')}
              </p>

              <p className="text-lg text-muted-foreground max-w-2xl mx-auto lg:mx-0 leading-relaxed">
                {t('hero.description')}
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Button size="lg" className="text-base px-8 py-6 h-auto min-h-[48px] font-medium bg-green-600 hover:bg-green-700" asChild>
                <Link href={`/${locale}/schedule-consultation`}>
                  <Heart className="w-5 h-5 mr-2" />
                  {tCta('scheduleConsultation')}
                </Link>
              </Button>

              <Button variant="outline" size="lg" className="text-base px-8 py-6 h-auto min-h-[48px] font-medium border-green-200 text-green-700 hover:bg-green-50" asChild>
                <Link href={`/${locale}/services`}>
                  {tCta('learnMore')}
                </Link>
              </Button>
            </div>

            {/* Process Indicators */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 pt-8 border-t border-border">
              <ProcessIndicator
                icon={<Users className="w-5 h-5" />}
                value={t('indicators.processes')}
                label={t('indicators.processesLabel')}
                status="automated"
              />
              <ProcessIndicator
                icon={<Activity className="w-5 h-5" />}
                value={t('indicators.efficiency')}
                label={t('indicators.efficiencyLabel')}
                status="optimized"
              />
              <ProcessIndicator
                icon={<Shield className="w-5 h-5" />}
                value={t('indicators.compliance')}
                label={t('indicators.complianceLabel')}
                status="active"
              />
            </div>
          </div>

          {/* Visual Column */}
          <div className="relative order-1 lg:order-2">
            {/* Digital Healthcare Dashboard Mockup */}
            <div className="relative aspect-[4/3] rounded-2xl overflow-hidden bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 border border-green-200 dark:border-green-800">
              <div className="absolute inset-0 flex items-center justify-center p-6">
                <div className="text-center space-y-4 w-full">
                  <div className="w-20 h-20 bg-green-600 rounded-full flex items-center justify-center mx-auto">
                    <Smartphone className="w-10 h-10 text-white" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-xl font-semibold text-foreground">{t('visual.title')}</h3>
                    <p className="text-base text-muted-foreground">{t('visual.subtitle')}</p>
                  </div>
                  
                  {/* Mock Process Cards */}
                  <div className="grid grid-cols-2 gap-3 mt-6">
                    <Card className="p-3 bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800">
                      <CardContent className="p-0 text-center">
                        <Bell className="w-6 h-6 text-blue-600 mx-auto mb-1" />
                        <div className="text-xs font-medium text-blue-700 dark:text-blue-300">Rufanlage</div>
                      </CardContent>
                    </Card>
                    <Card className="p-3 bg-purple-50 border-purple-200 dark:bg-purple-950 dark:border-purple-800">
                      <CardContent className="p-0 text-center">
                        <Brain className="w-6 h-6 text-purple-600 mx-auto mb-1" />
                        <div className="text-xs font-medium text-purple-700 dark:text-purple-300">Demenz Care</div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>

              {/* Floating Status Elements */}
              <div className="absolute top-4 right-4">
                <Card className="p-2 shadow-lg bg-white/90 dark:bg-gray-900/90">
                  <CardContent className="p-0 flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
                    <span className="text-xs font-medium">{t('visual.statusActive')}</span>
                  </CardContent>
                </Card>
              </div>

              <div className="absolute bottom-4 left-4">
                <Card className="p-2 shadow-lg bg-white/90 dark:bg-gray-900/90">
                  <CardContent className="p-0 flex items-center space-x-2">
                    <CheckCircle className="w-3 h-3 text-green-600" />
                    <span className="text-xs font-medium">{t('visual.statusOptimized')}</span>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Background Decorations */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-green-500/10 rounded-full blur-xl" />
            <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-green-600/10 rounded-full blur-xl" />
          </div>
        </div>
      </div>
    </section>
  );
}
